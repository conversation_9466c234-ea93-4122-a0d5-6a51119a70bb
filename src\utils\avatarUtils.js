// Helper function để lấy avatar đồng bộ cho tất cả các component
export const getUserAvatar = (user) => {
  const defaultAvatar = 'https://randomuser.me/api/portraits/men/1.jpg';
  if (!user) return defaultAvatar;
  
  // Kiểm tra avatar trực tiếp
  if (user.avatar && user.avatar.trim() !== '' && !user.avatar.includes('ui-avatars.com/api/?name=?')) {
    return user.avatar;
  }
  
  // Kiểm tra avatar trong user.user
  if (user.user && user.user.avatar && user.user.avatar.trim() !== '' && !user.user.avatar.includes('ui-avatars.com/api/?name=?')) {
    return user.user.avatar;
  }
  
  // Tạo avatar từ tên nếu có
  const userName = user.fullName || user.name || user.user?.fullName || user.user?.name || 'U';
  if (userName && userName !== 'U') {
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(userName)}&background=0074b7&color=ffffff&size=128`;
  }
  
  return defaultAvatar;
};

// Helper function để tạo avatar component với error handling
export const createAvatarImg = (user, altText, className = '', style = {}, onError = null) => {
  const defaultOnError = (e) => { 
    e.target.src = 'https://randomuser.me/api/portraits/men/1.jpg'; 
  };
  
  return {
    src: getUserAvatar(user),
    alt: altText || user?.fullName || user?.name || 'User Avatar',
    className,
    style: {
      objectFit: 'cover',
      ...style
    },
    onError: onError || defaultOnError
  };
};
